<cfcomponent output="false">

	<cffunction name="getGroupSetSelector" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="selectorID" type="string" required="yes" hint="id for hidden input control">
		<cfargument name="selectedValue" type="numeric" required="no" default="0" hint="selected groupSetID value">
		<cfargument name="selectedGroupSetName" type="string" required="no" default="" hint="selected group set name">
		<cfargument name="allowBlankOption" type="boolean" required="no" default="true">
		<cfargument name="inlinePreviewSectionID" type="string" required="no" default="" hint="provide only if selector is within a modal to avoid using modal again for group set preview/edit">
		<cfargument name="qryGroupSets" type="query" required="no" hint="override query">
		<cfargument name="getGroupSetDataFunc" type="string" required="no" default="" hint="override javascript function name that is present on the calling form">
		<cfargument name="usageMode" type="string" required="no" default="gsWidget" hint="override this value to include specific logic at places">
	
		<cfset var local = structNew()>
		<cfset local.data = { selectorID = arguments.selectorID, html = '' }>
		<cfset local.objAdmin = CreateObject("component","model.admin.admin")>
		
		<cfif NOT structKeyExists(arguments, "qryGroupSets")>
			<cfset local.objMemberGroupSets = CreateObject("component","model.admin.memberGroupSets.memberGroupSets")>
			<cfset local.qryGroupSets = local.objMemberGroupSets.getGroupSets(orgID=arguments.orgID)>
		<cfelse>
			<cfset local.qryGroupSets = arguments.qryGroupSets>
		</cfif>

		<cfset local.selectedGroupSetID = 0>
		<cfset local.selectedGroupSetLabel = "Choose Group Set">

		<cfif arguments.selectedValue GT 0 OR len(arguments.selectedGroupSetName)>
			<cfquery name="local.qryGroupSetSelected" dbtype="query">
				select groupSetID, groupSetName
				from [local].qryGroupSets
				<cfif arguments.selectedValue GT 0>
					where groupSetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.selectedValue#">
				<cfelse>
					where groupSetName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.selectedGroupSetName#">
				</cfif>
			</cfquery>

			<cfif local.qryGroupSetSelected.recordCount>
				<cfset local.selectedGroupSetID = local.qryGroupSetSelected.groupSetID>
				<cfset local.selectedGroupSetLabel = local.qryGroupSetSelected.groupSetName>
			</cfif>
		</cfif>

		<cfif len(arguments.inlinePreviewSectionID)>
			<cfset local.useInlinePreview = true>
			<cfset local.mode = "stream">
		<cfelse>
			<cfset local.useInlinePreview = false>
			<cfset local.mode = "direct">
		</cfif>
		
		<cfset local.editGroupSetLink = local.objAdmin.buildLinkToTool(toolType='MemberGroupSets',mca_ta='edit') & "&usageMode=#arguments.usageMode#&uniqueWidgetSelectorID=#arguments.selectorID#&mode=#local.mode#">
		<cfset local.previewGroupSetLink = local.objAdmin.buildLinkToTool(toolType='MemberGroupSets',mca_ta='previewGroupSet') & "&mode=stream">
			
		<cfsavecontent variable="local.data.html">
			<cfinclude template="dsp_groupsets_single.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getGroupSetsJSON" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.objAdmin = CreateObject("component","model.admin.admin")>
		<cfset local.objMemberGroupSets = CreateObject("component","model.admin.memberGroupSets.memberGroupSets")>

		<cfset local.qryGroupSets = local.objMemberGroupSets.getGroupSets(orgID=arguments.mcproxy_orgID)>

		<cfset local.returnStruct = {
			success = true,
			arravailablegroupsets = []
		}>

		<cftry>
			<cfloop query="local.qryGroupSets">
				<cfset arrayAppend(local.returnStruct.arravailablegroupsets, {
					groupsetid = local.qryGroupSets.groupSetID,
					groupsetname = local.qryGroupSets.groupSetName,
					isBeingUsed = (local.qryGroupSets.GroupSetBeingUsed GT 0)
				})>
			</cfloop>

			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.message = "Error retrieving group sets: #cfcatch.message#">
			</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getMultipleGroupSetSelector" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="selectorID" type="string" required="yes" hint="id for widget container">
		<cfargument name="siteResourceID" type="numeric" required="no" default="0" hint="siteResourceID for classifications (required for standard context)">
		<cfargument name="ID" type="numeric" required="no" default="0" hint="Context-specific ID (referralID for referrals, memberDirectoryID for member directory)">
		<cfargument name="selectedGSGridHeight" type="numeric" required="no" default="300" hint="height of selected group sets grid">
		<cfargument name="area" type="string" required="no" default="" hint="Optional area filter for project contexts ('details' or 'prospectcontact')">
		<cfargument name="editClassificationToolType" type="string" required="yes" hint="Tool type for edit classification link (e.g., 'MemberSettingsAdmin', 'projectAdmin')">

		<cfset var local = structNew()>
		<cfset local.data = { selectorID = arguments.selectorID, html = '' }>
		<cfset local.objAdmin = CreateObject("component","model.admin.admin")>
		<cfset local.isReferrals = (len(trim(arguments.selectorID)) AND findNoCase("referral", arguments.selectorID))>
		<cfset local.isMemberDirectory = (len(trim(arguments.selectorID)) AND findNoCase("memberDirectory", arguments.selectorID))>

		<!--- Build links for modal operations --->
		<cfif local.isReferrals>
			<!--- For referrals context, include referralID and selectorID parameters --->
			<cfset local.editGroupSetLink = local.objAdmin.buildLinkToTool(toolType='MemberGroupSets',mca_ta='edit') & "&usageMode=gsWidget&uniqueWidgetSelectorID=#arguments.selectorID#&mode=direct&referralID=#arguments.ID#&selectorID=#arguments.selectorID#">
		<cfelseif local.isMemberDirectory>
			<!--- For member directory context, include memberDirectoryID and selectorID parameters --->
			<cfset local.editGroupSetLink = local.objAdmin.buildLinkToTool(toolType='MemberGroupSets',mca_ta='edit') & "&usageMode=gsWidget&uniqueWidgetSelectorID=#arguments.selectorID#&mode=direct&memberDirectoryID=#arguments.ID#&selectorID=#arguments.selectorID#">
		<cfelse>
			<!--- For standard context, include siteResourceID and area parameters --->
			<cfset local.editGroupSetLink = local.objAdmin.buildLinkToTool(toolType='MemberGroupSets',mca_ta='edit') & "&usageMode=gsWidget&uniqueWidgetSelectorID=#arguments.selectorID#&mode=direct&siteResourceID=#arguments.siteResourceID#&area=#arguments.area#">
		</cfif>
		<cfset local.previewGroupSetLink = local.objAdmin.buildLinkToTool(toolType='MemberGroupSets',mca_ta='previewGroupSet') & "&mode=stream">

		<cfif local.isReferrals>
			<cfset local.editClassificationLink = local.objAdmin.buildLinkToTool(toolType='ReferralsAdmin',mca_ta='editClassification') & "&mode=direct&referralID=#arguments.ID#&selectorID=#arguments.selectorID#">
		<cfelseif local.isMemberDirectory>
			<cfset local.editClassificationLink = local.objAdmin.buildLinkToTool(toolType='#arguments.editClassificationToolType#',mca_ta='editClassification') & "&mode=direct&memberDirectoryID=#arguments.ID#&selectorID=#arguments.selectorID#">
		<cfelse>
			<cfset local.editClassificationLink = local.objAdmin.buildLinkToTool(toolType='#arguments.editClassificationToolType#',mca_ta='editClassification') & "&mode=direct&area=#arguments.area#&selectorID=#arguments.selectorID#">
		</cfif>
		
		<cfsavecontent variable="local.data.html">
			<cfinclude template="dsp_groupsets_multiple.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getAvailableAndSelectedGroupSetsJSON" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="siteResourceID" type="numeric" required="false" default="0">
		<cfargument name="ID" type="numeric" required="false" default="0" hint="Context-specific ID (referralID for referrals, memberDirectoryID for member directory)">
		<cfargument name="selectorID" type="string" required="false" default="">
		<cfargument name="area" type="string" required="false" default="" hint="Optional area filter for project contexts ('details' or 'prospectcontact')">

		<cfset var local = structNew()>
		<cfset local.returnStruct = {
			"success": true,
			"arrselectedgroupsets": [],
			"arravailablegroupsets": []
		}>

		<cftry>
			<cfset local.isReferrals = (len(trim(arguments.selectorID)) AND findNoCase("referral", arguments.selectorID))>
			<cfset local.isMemberDirectory = (len(trim(arguments.selectorID)) AND findNoCase("memberDirectory", arguments.selectorID))>

			<cfif local.isReferrals AND NOT arguments.ID>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.error = "ID (referralID) is required for referrals context">
				<cfreturn local.returnStruct>
			<cfelseif local.isMemberDirectory AND NOT arguments.ID>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.error = "ID (memberDirectoryID) is required for member directory context">
				<cfreturn local.returnStruct>
			<cfelseif NOT local.isReferrals AND NOT local.isMemberDirectory AND NOT arguments.siteResourceID>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.error = "siteResourceID is required for standard context">
				<cfreturn local.returnStruct>
			</cfif>

			<cfset local.objAdmin = CreateObject("component","model.admin.admin")>
			<cfset local.objMemberGroupSets = CreateObject("component","model.admin.memberGroupSets.memberGroupSets")>

			<!--- Get all available group sets for this organization --->
			<cfset local.qryAllGroupSets = local.objMemberGroupSets.getGroupSets(orgID=arguments.mcproxy_orgID)>

			<cfif local.isReferrals>
				<cfquery name="local.qrySelectedClassifications" datasource="#application.dsn.membercentral.dsn#">
					SELECT c.classificationID, c.name as classificationName, c.groupSetID, c.classificationOrder,
						   gs.groupSetName
					FROM ref_classifications c
					INNER JOIN ams_memberGroupSets gs ON c.groupSetID = gs.groupSetID
					WHERE c.referralID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ID#">
					  AND gs.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
					ORDER BY c.classificationOrder, c.name
				</cfquery>
			<cfelseif local.isMemberDirectory>
				<cfquery name="local.qrySelectedClassifications" datasource="#application.dsn.membercentral.dsn#">
					SELECT c.classificationID, c.name as classificationName, c.groupSetID, c.classificationOrder,
						   gs.groupSetName
					FROM ams_memberDirectoryClassifications c
					INNER JOIN ams_memberGroupSets gs ON c.groupSetID = gs.groupSetID
					WHERE c.memberDirectoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ID#">
					  AND gs.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
					ORDER BY c.classificationOrder, c.name
				</cfquery>
			<cfelse>
				<cfquery name="local.qrySelectedClassifications" datasource="#application.dsn.membercentral.dsn#">
					SELECT c.classificationID, c.name as classificationName, c.groupSetID, c.classificationOrder,
						   gs.groupSetName
					FROM ams_classifications c
					INNER JOIN ams_memberGroupSets gs ON c.groupSetID = gs.groupSetID
					WHERE c.siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">
					  AND gs.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
					  <cfif len(trim(arguments.area))>
					  AND c.area = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.area#">
					  </cfif>
					ORDER BY c.classificationOrder, c.name
				</cfquery>
			</cfif>

			<!--- Build selected group sets array --->
			<cfloop query="local.qrySelectedClassifications">
				<cfset arrayAppend(local.returnStruct.arrselectedgroupsets, {
					"classificationid": local.qrySelectedClassifications.classificationID,
					"classificationname": local.qrySelectedClassifications.classificationName,
					"groupsetid": local.qrySelectedClassifications.groupSetID,
					"groupsetname": local.qrySelectedClassifications.groupSetName,
					"classificationorder": local.qrySelectedClassifications.classificationOrder
				})>
			</cfloop>

			<!--- Build available group sets array (exclude already selected ones) --->
			<cfset local.selectedGroupSetIDs = "">
			<cfloop query="local.qrySelectedClassifications">
				<cfset local.selectedGroupSetIDs = listAppend(local.selectedGroupSetIDs, local.qrySelectedClassifications.groupSetID)>
			</cfloop>

			<cfloop query="local.qryAllGroupSets">
				<cfif NOT listFind(local.selectedGroupSetIDs, local.qryAllGroupSets.groupSetID)>
					<cfset arrayAppend(local.returnStruct.arravailablegroupsets, {
						"groupsetid": local.qryAllGroupSets.groupSetID,
						"groupsetname": local.qryAllGroupSets.groupSetName
					})>
				</cfif>
			</cfloop>

			<cfcatch type="any">
				<cfset local.returnStruct = {
					"success": false,
					"message": "Error loading group sets: " & cfcatch.message,
					"arrselectedgroupsets": [],
					"arravailablegroupsets": []
				}>
			</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="moveGroupSet" access="public" output="false" returntype="struct">
		<cfargument name="classificationID" type="numeric" required="true">
		<cfargument name="direction" type="string" required="true">
		<cfargument name="selectorID" type="string" required="false" default="">
		<cfargument name="mcproxy_memberID" type="numeric" required="false" default="0">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success": false, "message": "" }>

		<cftry>
			<cfset local.isReferrals = (len(trim(arguments.selectorID)) AND findNoCase("referral", arguments.selectorID))>
			<cfset local.isMemberDirectory = (len(trim(arguments.selectorID)) AND findNoCase("memberDirectory", arguments.selectorID))>

			<cfif local.isReferrals>
				<cfset local.recordedByMemberID = arguments.mcproxy_memberID GT 0 ? arguments.mcproxy_memberID : (structKeyExists(session, "cfcuser") AND structKeyExists(session.cfcuser, "memberData") ? session.cfcuser.memberData.memberID : 0)>

				<cfstoredproc procedure="dbo.ref_moveClassifications" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.classificationID#" />
					<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.recordedByMemberID#" />
					<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.direction#" />
				</cfstoredproc>
			<cfelseif local.isMemberDirectory>
				<!--- Get memberDirectoryID from classification --->
				<cfquery name="local.qryMemberDirectoryID" datasource="#application.dsn.membercentral.dsn#">
					SELECT memberDirectoryID
					FROM dbo.ams_memberDirectoryClassifications
					WHERE classificationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.classificationID#">
				</cfquery>

				<cfif local.qryMemberDirectoryID.recordCount>
					<cfstoredproc procedure="dbo.ams_moveMemberDirectoryClassifications" datasource="#application.dsn.membercentral.dsn#">
						<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.qryMemberDirectoryID.memberDirectoryID#" />
						<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.classificationID#" />
						<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.direction#" />
					</cfstoredproc>
				</cfif>
			<cfelse>
				<cfstoredproc procedure="dbo.ams_moveClassifications" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.classificationID#" />
					<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.direction#" />
				</cfstoredproc>
			</cfif>

			<cfset local.returnStruct.success = true>
			<cfset local.returnStruct.message = "Group set moved successfully">

			<cfcatch type="any">
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.message = "Error moving group set: " & cfcatch.message>
			</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

</cfcomponent>
