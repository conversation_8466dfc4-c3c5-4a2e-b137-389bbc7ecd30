USE membercentral;
GO

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Add deleteClassification method to ADMMEMBERDIRECTORY component for group set selector support
	DECLARE @componentID int, @adminViewRTFID int;

	IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
		DROP TABLE #ajaxComponentMethods;

	CREATE TABLE #ajaxComponentMethods (
		autoid int IDENTITY(1,1),
		methodName varchar(500),
		resourceTypeFunctionID int,
		methodID int
	);

	SELECT @adminViewRTFID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Members'),dbo.fn_getResourceFunctionID('Edit',dbo.fn_getResourceTypeID('Members')));

	INSERT INTO #ajaxComponentMethods(methodName, resourceTypeFunctionID)
	VALUES
		('deleteClassification', @adminViewRTFID);

	EXEC dbo.ajax_addComponentMethodRightsBulk
		@componentName='ADMMEMBERDIRECTORY',
		@requestCFC='model.admin.memberDirectory.memberDirectoryAdmin',
		@componentID=@componentID OUTPUT;

	IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
		DROP TABLE #ajaxComponentMethods;

	PRINT 'Successfully added deleteClassification method to ADMMEMBERDIRECTORY component';

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	PRINT 'Error: ' + ERROR_MESSAGE();
END CATCH
GO
