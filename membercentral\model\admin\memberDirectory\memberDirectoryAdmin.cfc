<cfcomponent extends="model.admin.admin" output="no">
	<cfset defaultEvent = 'controller'>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			// use resourceID of the site for security -------------------------------------------------- ::
			this.siteResourceID = arguments.event.getValue('mc_siteInfo.siteSiteResourceID');
			// Load Objects for page -------------------------------------------------------------------- ::
			this.objMemberFieldSet		= CreateObject("component","model.admin.MemberFieldSets.MemberFieldSets");

			// build quick links ------------------------------------------------------------------------ ::
			this.link.list 				= buildCurrentLink(arguments.event,"list");
			this.link.edit				= buildCurrentLink(arguments.event,"edit");
			this.link.editClassification= buildCurrentLink(arguments.event,"editClassification") & "&mode=direct";
			this.link.saveSettings		= buildCurrentLink(arguments.event,"saveSettings");
			this.link.saveClassification= buildCurrentLink(arguments.event,"saveClassification") & "&mode=direct";
			this.link.message 				= buildCurrentLink(arguments.event,"message");
			
			// method to run ---------------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('mca_ta')];
			// pass the argument collection to the current method and execute it. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>
	
	<cffunction name="getMemberDirectory" access="public" output="false" returntype="query">
		<cfargument name="memberDirectoryID" type="numeric">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.qryData" datasource="#application.dsn.memberCentral.dsn#">
			set nocount on;

			declare @RTID int;
			select @RTID = dbo.fn_getResourceTypeID('Community');

			SELECT ai.applicationInstanceName, sr.siteResourceID, md.memberDirectoryID, md.applicationInstanceID, 
				md.maxsearchresults, md.recordsperpage, md.inactivemembersVisible, md.distanceLabel, 
				md.includeContactFormOnDetails, md.bccContactForm, md.enableStats, md.enableCompare, 
				isNull(md.showPhotoRule,1) as showPhotoRule, searchContent.contentID as searchContentID,
				searchContent.contentTitle as searchContentTitle, searchContent.contentDesc as searchContentDesc,
				searchContent.rawContent as searchContent, 
				memberDirectoryName = ai.applicationInstanceName + 
					case 
					WHEN communityInstances.applicationInstanceName is not null THEN ' (' + communityInstances.applicationInstanceName + ')'
					ELSE ''
					END,
				contactContent.contentID as contactContentID, contactContent.rawContent as contactContent
			FROM dbo.ams_memberDirectories as md
			INNER JOIN dbo.cms_applicationInstances ai ON md.applicationInstanceID = ai.applicationInstanceID
				AND ai.siteID = #application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).siteID#
			INNER JOIN dbo.cms_siteResources sr ON ai.siteResourceID = sr.siteResourceID
			INNER JOIN dbo.cms_siteResources AS parentResource ON parentResource.siteResourceID = sr.parentSiteResourceID
			left outer join dbo.cms_siteResources AS grandparentResource
				inner join dbo.cms_applicationInstances AS CommunityInstances 
					on communityInstances.siteResourceID = grandParentResource.siteResourceID
					on grandparentResource.siteResourceID = parentResource.parentSiteResourceID
					and grandparentResource.resourceTypeID = @RTID
			CROSS APPLY dbo.fn_getContent(md.searchContentID,#application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).defaultLanguageID#) AS searchContent
			CROSS APPLY dbo.fn_getContent(md.contactContentID,#application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).defaultLanguageID#) AS contactContent
			WHERE md.memberDirectoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberDirectoryID#">;
		</cfquery>
		
		<cfreturn local.qryData>
	</cffunction>

	<cffunction name="getMemberDirectoryIDFromAppID" access="public" output="false" returntype="numeric">
		<cfargument name="applicationInstanceID" type="numeric">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryData" datasource="#application.dsn.memberCentral.dsn#">
			SELECT memberDirectoryID
			FROM dbo.ams_memberDirectories
			WHERE applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.applicationInstanceID#">
		</cfquery>
		
		<cfreturn local.qryData.memberDirectoryID>
	</cffunction>

	<cffunction name="list" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.listMemberDirectoryLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberDirectoryJSON&meth=getMemberDirectories&mode=stream">
		<cfset local.appTypeID = application.objApplications.getApplicationTypeIDFromName('Members')>
		<cfset local.addpageLink = CreateObject("component","model.admin.admin").buildLinkToTool(toolType='PageAdmin',mca_ta='addPage')>
		<cfset local.canAddInstance = CreateObject("component","model.admin.pages.appCreationProcess").canAddAppInstance(siteID=arguments.event.getValue('mc_siteInfo.siteID'), applicationTypeID=local.appTypeID)>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_memberDirectoryList.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="edit" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.objFieldSetSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector");
			local.rc = arguments.event.getCollection();
			
			local.classificationsListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberDirectoryJSON&meth=getClassifications&mode=stream&mdID=#arguments.event.getValue('mdID')#";
			local.currentSortListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberDirectoryJSON&meth=getCurrentSortOptionsList&mode=stream&mdid=#arguments.event.getValue('mdID')#";
			local.availableSortListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberDirectoryJSON&meth=getAvailableSortOptionsList&mode=stream&mdid=#arguments.event.getValue('mdID')#";

			local.siteID = arguments.event.getValue('mc_siteInfo.siteID');
			local.mdID 	= arguments.event.getValue('mdID',0);
			local.baseLink = this.link.edit & '&mdID=' & arguments.event.getValue('mdID');
			local.mdSettings = getMemberDirectory(arguments.event.getValue('mdID'));
			local.siteResourceID = local.mdSettings.siteResourceID;
			local.resourceName = local.mdSettings.memberDirectoryName;
			local.appInstanceSettings = super.getInstanceSettings(this.appInstanceID);

			local.strMemberDirectoryFieldSetsResults = local.objFieldSetSelector.getMultipleFieldSetSelector(
				selectorID="fsSelectorForResults",
				getFieldSetDataFunc="getAvailableAndSelectedFieldSetsForResults",
				addFieldSetUsageFunc="createMemberFieldUsageForResults",
				removeFieldSetUsageFunc="removeMemberFieldUsage",
				hasPermissionAction=true,
				hasOrderingAction=true,
				orderFieldSetFunc="moveMemberFieldFSRow");
			
			local.strMemberDirectoryFieldSetsDetails = local.objFieldSetSelector.getMultipleFieldSetSelector(
				selectorID="fsSelectorForDetails",
				getFieldSetDataFunc="getAvailableAndSelectedFieldSetsForDetails",
				addFieldSetUsageFunc="createMemberFieldUsageForDetails",
				removeFieldSetUsageFunc="removeMemberFieldUsage",
				hasPermissionAction=true,
				hasOrderingAction=true,
				orderFieldSetFunc="moveMemberFieldFSRow");

			local.strMemberDirectoryFieldSetsComparisons = local.objFieldSetSelector.getMultipleFieldSetSelector(
				selectorID="fsSelectorForCompare",
				getFieldSetDataFunc="getAvailableAndSelectedFieldSetsForCompare",
				addFieldSetUsageFunc="createMemberFieldUsageForCompare",
				removeFieldSetUsageFunc="removeMemberFieldUsage",
				hasPermissionAction=true,
				hasOrderingAction=true,
				orderFieldSetFunc="moveMemberFieldFSRow");

			// Build breadCrumb Trail ------------------------------------------------------------------- ::
			appendBreadCrumbs(arguments.event,{ link='', text=encodeForHTML('Member Directory: #local.resourceName#') });
		</cfscript>
		
		<cfset local.data.usageDetails 	= this.objMemberFieldSet.getUsageDetails(local.siteResourceID)>
		
		<cfquery name="local.data.searchUsageDetails" dbtype="query">
			select *
			from [local].[data].usageDetails
			where area = 'search'
		</cfquery>
		
		<cfset local.data.searchFieldSet 	= getMemberDirectoryFieldsetID(local.siteResourceID,'search')>

		<cfset local.strSearchFSSelector = local.objFieldSetSelector.getFieldSetSelector(siteID=local.siteID, selectorID="SearchFieldSet", selectedValue=local.data.searchFieldSet)/>
		
		<cfquery name="local.qrySocialNetworkCheck" datasource="#application.dsn.memberCentral.dsn#">
			select sn.socialNetworkID
			from sn_socialNetworks sn
			inner join cms_applicationInstances ai on ai.applicationInstanceID = sn.applicationInstanceID and ai.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.siteID#">
			inner join cms_siteResources sr on sr.siteResourceID = ai.siteResourceID
			inner join cms_siteResourceStatuses srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
		</cfquery>

		<cfif local.qrySocialNetworkCheck.recordcount GT 0>		
			<cfset local.hasSocialNetwork = true>
		<cfelse>
			<cfset local.hasSocialNetwork = false>
		</cfif>
		<!--- Create group set selector widget for member directory classifications --->
		<cfset local.objGroupSetSelector = createObject("component","model.admin.common.modules.groupSetSelector.groupSetSelector")>
		<cfset local.memberDirectoryGroupSetWidget = local.objGroupSetSelector.getMultipleGroupSetSelector(
			siteID = arguments.event.getValue('mc_siteInfo.siteID'),
			orgID = arguments.event.getValue('mc_siteInfo.orgID'),
			selectorID = "memberDirectoryClassificationsGroupSets",
			ID = arguments.event.getValue('mdID'),
			selectedGSGridHeight = 350,
			editClassificationToolType = "MemberDirectoryAdmin"
		)>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>Manage #local.resourceName#</h4>
				<cfinclude template="frm_edit.cfm">
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="editClassification" access="public" output="false" returnType="struct">
		<cfargument name="Event" type="any">
	
		<cfset var local = structNew()>
		<cfset local.gsid = arguments.event.getValue('groupSetID',0)>
		<cfset local.selectorID = arguments.event.getValue('selectorID','')>

		<cfquery name="local.qryClassification" datasource="#application.dsn.memberCentral.dsn#">
			SELECT classificationid, name, allowSearch, memberDirectoryID, showGroupImage, imagePlacement, showInSearchResults, groupSetID, classificationOrder, showInSearchDetail, showGroupImageInSearchDetail
			FROM dbo.ams_memberDirectoryClassifications 
			WHERE classificationID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('cid',0)#">
		</cfquery>
		<cfset local.qryGroupSets = CreateObject("component","model.admin.MemberGroupSets.MemberGroupSets").getGroupSets(orgID=arguments.event.getValue('mc_siteInfo.orgID'))>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_classification.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="saveClassification" access="public" output="false" returnType="struct">
		<cfargument name="Event" type="any">
	
		<cfset var local = structNew()>

		<cfquery name="local.qryData" datasource="#application.dsn.memberCentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				<cfif arguments.event.getValue('classificationID',0) gt 0>
					UPDATE dbo.ams_memberDirectoryClassifications
					SET name = nullif(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('nameOverride','')#">,''),
						allowSearch = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('allowSearch',0)#">,
						showInSearchResults = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showInSearchResults',0)#">,
						showInSearchDetail = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showInSearchDetail',0)#">,
						showGroupImage = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showGroupImage',0)#">,
						imagePlacement = <cfqueryparam cfsqltype="cf_sql_varchar" null="#iif(Len(arguments.event.getValue('imagePlacement','')),false,true)#" value="#arguments.event.getValue('imagePlacement','')#">,
						showGroupImageInSearchDetail = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showGroupImageInSearchDetail',0)#">,
						groupSetID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('groupSetID',0)#">
					WHERE classificationID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('classificationID',0)#">;
				<cfelse>				
					declare @newOrderItem int;
					
					select @newOrderItem = case when max(classificationOrder) is null then 1 else max(classificationOrder) + 1 end
					from dbo.ams_memberDirectoryClassifications
					where memberDirectoryID = <cfqueryparam value="#arguments.event.getValue('memberDirectoryID',0)#" cfsqltype="cf_sql_integer">;
									
					INSERT INTO dbo.ams_memberDirectoryClassifications(name,allowSearch,memberDirectoryID,showInSearchResults,showInSearchDetail,showGroupImage,imagePlacement,groupSetID,classificationOrder)
					VALUES(
						nullif(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('nameOverride','')#">,''),
						<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('allowSearch',0)#">,
						<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('memberDirectoryID',0)#">,
						<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showInSearchResults',0)#">,
						<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showInSearchDetail',0)#">,
						<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showGroupImage',0)#">,
						<cfqueryparam cfsqltype="cf_sql_varchar" null="#iif(Len(arguments.event.getValue('imagePlacement','')),false,true)#" value="#arguments.event.getValue('imagePlacement','')#">,
						<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('groupSetID',0)#">,
						@newOrderItem
					);
				</cfif>

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script>
					top.reloadClassifications();
					top.MCModalUtils.hideModal();
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveSettings" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.showPhotoRule = arguments.event.getValue('showPhotoRule',0)>

		<cfquery name="local.qryData" datasource="#application.dsn.memberCentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				declare @applicationInstanceID int, @useID int, @origfieldSetID int, @newfieldSetID int, @area varchar(20), 
					@siteResourceID int, @oldenableCompare bit, @memberDirectoryID int, @comparefieldsetID int;
				set @applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('applicationInstanceID')#">;
				<cfif len(trim(arguments.event.getValue('useID'))) AND val(arguments.event.getValue('useID')) >
					SET @useID = <cfqueryparam value="#arguments.event.getValue('useID')#" cfsqltype="CF_SQL_INTEGER">;
				</cfif>
				SET @origfieldSetID = <cfqueryparam value="#val(arguments.event.getValue('origSearchFieldSet'))#" cfsqltype="CF_SQL_INTEGER">;
				SET @newfieldSetID = <cfqueryparam value="#val(arguments.event.getValue('SearchFieldSet'))#" cfsqltype="CF_SQL_INTEGER">;
				SET @memberDirectoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mdID')#">;

				SELECT @siteResourceID = siteResourceID
				FROM dbo.cms_applicationInstances
				WHERE applicationInstanceID = @applicationInstanceID;

				SELECT @oldenableCompare = enableCompare
				from dbo.ams_memberDirectories
				WHERE memberDirectoryID = @memberDirectoryID;

				BEGIN TRAN;
					UPDATE dbo.cms_applicationInstances
					SET	applicationInstanceName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('applicationInstanceName')#">
					WHERE applicationInstanceID = @applicationInstanceID;
				
					UPDATE dbo.ams_memberDirectories
					SET recordsPerPage = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('recordsPerPage')#">,
						maxSearchResults = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('maxSearchResults')#">,
						<cfif local.showPhotoRule neq 0>
							showPhotoRule = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('showPhotoRule')#">,
						</cfif>
						distanceLabel = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('distanceLabel')#">,
						inactiveMembersVisible = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('inactiveMembersVisible',0)#">,
						includeContactFormOnDetails = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('includeContactFormOnDetails',0)#">,
						bccContactForm = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('bccContactForm')#">,
						enableStats = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('enableStats',0)#">,
						enableCompare = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('enableCompare',0)#">
					WHERE memberDirectoryID = @memberDirectoryID;
				
					IF @origfieldSetID <> @newfieldSetID BEGIN
						<cfif len(trim(arguments.event.getValue('useID'))) AND val(arguments.event.getValue('useID')) >
							UPDATE dbo.ams_memberFieldUsage
							SET fieldsetID = @newfieldSetID
							WHERE useID = @useID;
						<cfelse>
							INSERT INTO ams_memberFieldUsage(siteResourceID,fieldsetID,area,fieldSetOrder)
							VALUES(@siteResourceID,@newfieldSetID,'search',1);
						</cfif>
					END

					<!--- if disabling compare, ensure there are no fieldsets in use so they can deleted if necessary --->
					<cfif arguments.event.getValue('enableCompare',0) is 0>
						IF @oldenableCompare = 1 BEGIN
							declare @tblCompareUse TABLE (useID int, useSiteResourceID int);

							insert into @tblCompareUse (useID, useSiteResourceID)
							SELECT u.useID, u.useSiteResourceID
							FROM dbo.ams_memberFieldUsage as u
							INNER JOIN dbo.ams_memberFieldSets fs on fs.fieldSetID = u.fieldSetID
							INNER JOIN dbo.cms_siteResources sr on u.useSiteResourceID = sr.siteResourceID 
							INNER JOIN dbo.cms_siteResourceStatuses srs ON sr.siteResourceStatusID = srs.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
							WHERE u.siteResourceID = @siteResourceID
							AND u.area = 'compare';

							-- delete siteresourceID
							UPDATE sr
							SET sr.siteResourceStatusID = 3
							FROM dbo.cms_siteResources as sr
							INNER JOIN @tblCompareUse as tbl on tbl.useSiteResourceID = sr.siteResourceID;

							-- delete usage
							DELETE FROM dbo.ams_memberFieldUsage
							WHERE useID in (select useID from @tblCompareUse);
						END

					<!--- if enabling compare, copy fieldsets from results usage --->
					<cfelseif arguments.event.getValue('enableCompare',0) is 1>
						IF @oldenableCompare = 0 BEGIN
							declare @tblCompareFS TABLE (fieldSetID int);

							insert into @tblCompareFS (fieldSetID)
							SELECT fs.fieldSetID
							FROM dbo.ams_memberFieldUsage as u
							INNER JOIN dbo.ams_memberFieldSets fs on fs.fieldSetID = u.fieldSetID
							INNER JOIN dbo.cms_siteResources sr on u.useSiteResourceID = sr.siteResourceID 
							INNER JOIN dbo.cms_siteResourceStatuses srs ON sr.siteResourceStatusID = srs.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
							WHERE u.siteResourceID = @siteResourceID
							AND u.area = 'results';

							select @comparefieldsetID = min(fieldSetID) from @tblCompareFS;
							while @comparefieldsetID is not null begin
								EXEC dbo.ams_createMemberFieldUsage @siteResourceID=@siteResourceID, @fieldsetID=@comparefieldsetID, 
									@area='compare', @createSiteResourceID=1, @useID=@useID OUTPUT;
								select @comparefieldsetID = min(fieldSetID) from @tblCompareFS where fieldSetID > @comparefieldsetID;
							end
						END
					</cfif>					

					EXEC dbo.cms_updateContent @contentID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('contactContentID')#">,
						@languageID=1, @isHTML=1, @contentTitle='', @contentDesc='', 
						@rawContent=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getValue('contactContent')#">,
						@memberID=#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).orgID)#;
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cflocation url="#this.link.edit#&mdID=#arguments.event.getValue('mdID')#" addtoken="false">
	</cffunction>	
	
	<cffunction name="getMemberDirectoryFieldsetID" access="public" output="no" returntype="numeric">
		<cfargument name="siteResourceID" type="numeric" required="yes" default="0">
		<cfargument name="area" type="string" required="yes">
		
		<cfset var qryFieldSet = "">
		
		<!--- validate areas --->
		<cfif NOT listFindNoCase("search,results",arguments.area)>
			<cfset arguments.area = "search">
		</cfif>
		
		<!--- get fieldset --->
		<cfquery name="qryFieldSet" datasource="#application.dsn.membercentral.dsn#">
			SELECT  fieldSetID
			FROM	dbo.ams_memberFieldUsage as mfu
			WHERE	mfu.siteResourceID = <cfqueryparam value="#arguments.siteResourceID#" cfsqltype="CF_SQL_INTEGER">
				AND mfu.area = <cfqueryparam value="#arguments.area#" cfsqltype="cf_sql_varchar">
			ORDER BY	fieldSetOrder
		</cfquery>
		<cfif qryFieldSet.recordCount>
			<cfreturn qryFieldSet.fieldSetID>
		<cfelse>
			<cfreturn 0>
		</cfif>
	</cffunction>
	
	<!--- MemberDirectory AJAX CALLS--->
	<cffunction name="removeMD" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric">
		<cfargument name="memberDirectoryID" type="numeric">
		<cfargument name="siteResourceID" type="numeric">

		<cfset local = structNew()>
		<cfset local.tmpRights = buildRightAssignments(siteResourceID=arguments.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>

		<cftry>
			<cfif NOT local.tmpRights.Delete>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery name="local.qryRemoveMD" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">,
					@memberDirectoryID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberDirectoryID#">,
					@siteResourceID int;

				SELECT @siteResourceID = sr.parentSiteResourceID
				FROM dbo.ams_memberDirectories AS md
				INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = md.applicationInstanceID 
					AND ai.siteID = @siteID
				INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID AND sr.siteResourceID = ai.siteResourceID
				WHERE md.memberDirectoryID = @memberDirectoryID;

				EXEC dbo.cms_deleteSiteResourceAndChildren @siteID=@siteID, @siteResourceID=@siteResourceID;
			</cfquery>

			<cfset local.returnStruct.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<!--- SortOptions AJAX CALLS--->
	<cffunction name="addSO" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric">
		<cfargument name="memberDirectoryID" type="numeric">
		<cfargument name="memberDirectorySRID" type="numeric">
		<cfargument name="type" type="numeric">
		<cfargument name="typeID" type="numeric">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasEditMemberDirectoryRights(siteID=arguments.mcproxy_siteID,siteResourceID=arguments.memberDirectorySRID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery name="local.qryData" datasource="#application.dsn.memberCentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
				
					declare @memberDirectoryID int, @maxOrder int, 	@direction varchar(4);
				
					SET @memberDirectoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberDirectoryID#">;
					<cfif arguments.type EQ 1>
						SET @direction = 'ASC';
					<cfelse>
						SET @direction = 'DESC';
					</cfif>
				
					SELECT @maxOrder = MAX(isNull(sortOrder,0)) + 1
					FROM dbo.ams_memberDirectorySorts
					WHERE memberDirectoryID = @memberDirectoryID;
				
					BEGIN TRAN;
						INSERT INTO dbo.ams_memberDirectorySorts (memberDirectoryID, <cfif arguments.type EQ 1>sortDefaultID<cfelse>userGroupID</cfif>, sortOrder, AscOrDesc)
						VALUES (@memberDirectoryID, <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.typeID#">, @maxOrder, @direction);

						EXEC dbo.ams_reorderMemberDirectorySorts @memberDirectoryID=@memberDirectoryID;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="switchSODirection" access="public" output="false" returntype="struct">
		<cfargument name="memberDirectorySortID" type="numeric">
		<cfargument name="direction" type="string">

		<cfset var local = structNew()>

		<cftry>
			<cfquery name="local.qryData" datasource="#application.dsn.memberCentral.dsn#">
				UPDATE dbo.ams_memberDirectorySorts
				SET AscORDesc = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.direction#">
				WHERE memberDirectorySortID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberDirectorySortID#">
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="moveSO" access="public" output="false" returntype="struct">
		<cfargument name="memberDirectorySortID" type="numeric">
		<cfargument name="dir" type="string" required="true">
		<cfset var local = structNew()>

		<cftry>
			<cfquery name="local.qryData" datasource="#application.dsn.memberCentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @currentOrderNum int, @memberDirectoryID int;
					
					SELECT @currentOrderNum = sortOrder, @memberDirectoryID = memberDirectoryID
					FROM dbo.ams_memberDirectorySorts 
					WHERE memberDirectorySortID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberDirectorySortID#">;
				
					BEGIN TRAN;
						<cfif arguments.dir EQ 'UP'>
							UPDATE dbo.ams_memberDirectorySorts
							SET sortOrder = sortOrder + 1
							WHERE memberDirectoryID = @memberDirectoryID
							AND sortOrder >= @currentOrderNum - 1;
						
							UPDATE dbo.ams_memberDirectorySorts
							SET sortOrder = sortOrder - 2
							WHERE memberDirectorySortID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberDirectorySortID#">;
						<cfelse>
							UPDATE dbo.ams_memberDirectorySorts
							SET sortOrder = sortOrder - 1
							WHERE memberDirectoryID = @memberDirectoryID
							AND sortOrder <= @currentOrderNum + 1;
					
							UPDATE dbo.ams_memberDirectorySorts
							SET sortOrder = sortOrder + 2
							WHERE memberDirectorySortID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberDirectorySortID#">;
						</cfif>
						EXEC dbo.ams_reorderMemberDirectorySorts @memberDirectoryID	= @memberDirectoryID;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="removeSO" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="memberDirectorySRID" type="numeric">
		<cfargument name="memberDirectorySortID" type="numeric">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasEditMemberDirectoryRights(siteID=arguments.mcproxy_siteID,siteResourceID=arguments.memberDirectorySRID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery name="local.qryData" datasource="#application.dsn.memberCentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
				
					DECLARE @memberDirectoryID int;

					SELECT @memberDirectoryID = memberDirectoryID 
					FROM dbo.ams_memberDirectorySorts
					WHERE memberDirectorySortID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberDirectorySortID#">;
				
					BEGIN TRAN;
						DELETE FROM dbo.ams_memberDirectorySorts
						WHERE memberDirectorySortID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberDirectorySortID#">;
				
						EXEC dbo.ams_reorderMemberDirectorySorts @memberDirectoryID	= @memberDirectoryID;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="removeClassification" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="memberDirectoryID" type="numeric" required="true" />
		<cfargument name="memberDirectorySRID" type="numeric" required="true">
		<cfargument name="classificationID" type="numeric" required="true">

		<cfset var local = structNew()>
		
		<cftry>
			<cfif not hasEditMemberDirectoryRights(siteID=arguments.mcproxy_siteID,siteResourceID=arguments.memberDirectorySRID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery name="local.qryData" datasource="#application.dsn.memberCentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					declare @memberDirectoryID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberDirectoryID#">;
			
					BEGIN TRAN;
						delete from dbo.ams_memberDirectoryClassifications
						where memberDirectoryID = @memberDirectoryID
						AND classificationID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.classificationID#">;
			
						exec dbo.ams_reorderMemberDirectoryClassifications @memberDirectoryID = @memberDirectoryID;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteClassification" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="classificationID" type="numeric" required="true">
		<cfargument name="memberDirectoryID" type="numeric" required="false" default="0">
		<cfargument name="memberDirectorySRID" type="numeric" required="false" default="0">

		<cfset var local = structNew()>

		<cftry>
			<!--- Get memberDirectoryID and siteResourceID if not provided --->
			<cfif arguments.memberDirectoryID EQ 0 OR arguments.memberDirectorySRID EQ 0>
				<cfquery name="local.qryClassificationInfo" datasource="#application.dsn.membercentral.dsn#">
					SELECT mdc.memberDirectoryID, md.applicationInstanceID, ai.siteResourceID
					FROM dbo.ams_memberDirectoryClassifications mdc
					INNER JOIN dbo.ams_memberDirectories md ON md.memberDirectoryID = mdc.memberDirectoryID
					INNER JOIN dbo.cms_applicationInstances ai ON ai.applicationInstanceID = md.applicationInstanceID
					WHERE mdc.classificationID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.classificationID#">
				</cfquery>

				<cfif local.qryClassificationInfo.recordCount>
					<cfset arguments.memberDirectoryID = local.qryClassificationInfo.memberDirectoryID>
					<cfset arguments.memberDirectorySRID = local.qryClassificationInfo.siteResourceID>
				</cfif>
			</cfif>

			<!--- Call the existing removeClassification method --->
			<cfset local.result = removeClassification(
				mcproxy_siteID = arguments.mcproxy_siteID,
				memberDirectoryID = arguments.memberDirectoryID,
				memberDirectorySRID = arguments.memberDirectorySRID,
				classificationID = arguments.classificationID
			)>

			<cfset local.data = local.result>
		<cfcatch type="Any">
			<cfset local.data = { success = false, message = "Error deleting classification: " & cfcatch.message }>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doMoveClassification" access="public" output="false" returntype="struct" hint="Re-Order Fields">
		<cfargument name="memberDirectoryID" type="numeric" required="true" />
		<cfargument name="classificationID" type="numeric" required="true" />
		<cfargument name="dir" type="string" required="true">

		<cfset var local = structNew() />

		<cftry>
			<cfstoredproc procedure="ams_moveMemberDirectoryClassifications" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" value="#arguments.memberDirectoryID#" cfsqltype="cf_sql_integer" />
				<cfprocparam type="In" value="#arguments.classificationID#" cfsqltype="cf_sql_integer" />
				<cfprocparam type="In" cfsqltype="cf_sql_varchar" value="#arguments.dir#">
			</cfstoredproc>
			
			<cfset local.data.success = true />
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data />
	</cffunction>

	<cffunction name="hasEditMemberDirectoryRights" access="private" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=arguments.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.siteID)>

		<cfreturn local.tmpRights.Edit is 1>
	</cffunction>

</cfcomponent>
