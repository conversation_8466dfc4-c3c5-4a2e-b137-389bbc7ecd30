<cfsavecontent variable="local.classificationJS">
	<cfoutput>
	<script language="javascript">
		function showImagePlacement(showImage) {
			showGroupImage_Value = document.getElementById('showGroupImage').value;
			showGroupImageInSearchDetail_Value = document.getElementById('showGroupImageInSearchDetail').value;
			if (showGroupImage_Value==1 || showGroupImageInSearchDetail_Value==1) $('##imagePlacementArea').show();
			else $('##imagePlacementArea').hide();
		}
		function startsWithAlpha(myString){
			var c = myString.charAt(0);
			if( isNaN(c) ) return true;
			else return false;
		}
		function validateClassification() {
			var xName = $('##nameOverride').val();
			var sfs = $('##groupSetID').val();
			mca_hideAlert('err_classification');

			var arrReq = new Array();
			if (xName.length > 0) {
				if(!mca_isAlphaNumeric(xName)){ arrReq[arrReq.length] = 'The Classification Name Override can only contain letters and numbers.'; }
				if(!startsWithAlpha(xName)){ arrReq[arrReq.length] = 'The Classification Name Override must start with a letter.'; }
			}
			if (sfs == '') { arrReq[arrReq.length] = 'Choose a Group Set for this classification.'; }

			if (arrReq.length > 0) {
				mca_showAlert('err_classification', arrReq.join('<br/>'), true);
				return false;
			}
			top.$('##btnMCModalSave').prop('disabled', true);
			return true;
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.classificationJS)#">

<cfoutput>
<div id="err_classification" class="alert alert-danger mb-2 mt-2 d-none"></div>
<div class="row-fluid p-3">
	<form name="frmClassification" id="frmClassification" method="post" action="#this.link.saveClassification#" onsubmit="return validateClassification();">
		<input type="hidden" name="memberDirectoryID" value="#arguments.event.getValue('mdid',0)#">
		<input type="hidden" name="classificationID" value="#local.qryClassification.classificationID#">
		<input type="hidden" name="groupSetID" value="#local.gsid#" />
		<input type="hidden" name="selectorID" value="#local.selectorID#" />
		<div class="form-group">
			<div class="form-label-group">
				<select name="allowSearch" id="allowSearch" class="form-control">
					<option value="0"<cfif NOT val(local.qryClassification.allowSearch)> SELECTED</cfif>>No</option>
					<option value="1"<cfif val(local.qryClassification.allowSearch)> SELECTED</cfif>>Yes</option>
				</select>
				<label for="allowSearch">Display on Search Form *</label>
			</div>
		</div>
		<div class="form-group">
			<div class="form-label-group">
				<select name="showInSearchResults" id="showInSearchResults" class="form-control">
					<option value="0"<cfif NOT val(local.qryClassification.showInSearchResults)> SELECTED</cfif>>No</option>
					<option value="1"<cfif val(local.qryClassification.showInSearchResults)> SELECTED</cfif>>Yes</option>
				</select>
				<label for="showInSearchResults">Display on Search Results *</label>
			</div>
		</div>
		<div class="form-group">
			<div class="form-label-group">
				<select name="showInSearchDetail" id="showInSearchDetail"class="form-control">
					<option value="0"<cfif NOT val(local.qryClassification.showInSearchDetail)> SELECTED</cfif>>No</option>
					<option value="1"<cfif val(local.qryClassification.showInSearchDetail)> SELECTED</cfif>>Yes</option>
				</select>
				<label for="showInSearchDetail">Display on Search Detail *</label>
			</div>
		</div>
		<div class="form-group">
			<div class="form-label-group">
				<select name="showGroupImage" id="showGroupImage" onChange="showImagePlacement()" class="form-control">
					<option value="0"<cfif NOT val(local.qryClassification.showGroupImage)> SELECTED</cfif>>No</option>
					<option value="1"<cfif val(local.qryClassification.showGroupImage)> SELECTED</cfif>>Yes</option>
				</select>
				<label for="showGroupImage">Display Group Image in Results * (if any are defined)
				</label>
			</div>
		</div>
		<div class="form-group">
			<div class="form-label-group">
				<select name="showGroupImageInSearchDetail" id="showGroupImageInSearchDetail" onChange="showImagePlacement()" class="form-control">
					<option value="0"<cfif NOT val(local.qryClassification.showGroupImageInSearchDetail)> SELECTED</cfif>>No</option>
					<option value="1"<cfif val(local.qryClassification.showGroupImageInSearchDetail)> SELECTED</cfif>>Yes</option>
				</select>
				<label for="showGroupImageInSearchDetail">Display Group Image in Search Detail * (if any are defined)
				</label>
			</div>
		</div>
		<div class="form-group">
			<div class="form-label-group">
				<select name="imagePlacement"  id="imagePlacement" class="form-control">
					<option value="under"<cfif local.qryClassification.imagePlacement eq "under"> SELECTED</cfif>>Under Classifications List</option>
					<option value="above"<cfif local.qryClassification.imagePlacement eq "above"> SELECTED</cfif>>Above Member Profile</option>
					<option value="below"<cfif local.qryClassification.imagePlacement eq "below"> SELECTED</cfif>>Below Member Profile</option>
				</select>
				<label for="imagePlacement">Group Image Placement * (if any are defined)
				</label>
			</div>
		</div>
		<div class="form-label-group">
		<select id="groupSet" name="groupSet" onchange="$('##groupSetID').val($(this).val());" class="form-control" <cfif local.gsid NEQ 0>disabled</cfif>>
			<option value=""></option>
			<cfloop query="local.qryGroupSets">
				<option value="#local.qryGroupSets.groupSetID#"<cfif local.qryClassification.groupSetID is local.qryGroupSets.groupSetID OR local.gsid eq local.qryGroupSets.groupSetID> SELECTED</cfif>>#left(local.qryGroupSets.groupSetName,70)#<cfif len(local.qryGroupSets.groupSetName) gt 70>...</cfif></option>
			</cfloop>
		</select>
		<label for="groupSet">Group Set for Classification * (if any are defined)</label>
	</div>
		<div class="form-group">
			<div class="form-label-group">
				<input type="text" name="nameOverride" id="nameOverride" value="#local.qryClassification.name#" class="form-control">
				<label for="nameOverride">Classification Name Override</label>
			</div>
		</div>
		<div class="text-right d-none">
			<button type="submit" name="btnSaveClassification" id="btnSaveClassification" class="btn btn-sm btn-primary">Save Details</button>
		</div>
	</form>
</div>
</cfoutput>